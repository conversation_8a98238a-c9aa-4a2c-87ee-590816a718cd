<?php

namespace Webkul\Shop\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Webkul\CMS\Repositories\PageRepository;
use Webkul\Shop\Http\Controllers\Controller;

class CMSController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\CMS\Repositories\PageRepository  $pageRepository
     * @return void
     */
    public function __construct(
        protected PageRepository $pageRepository
    ) {}

    /**
     * Get CMS pages.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        // Get current channel and locale
        $channel = core()->getCurrentChannel();
        $locale = app()->getLocale();
        
        // Prepare filters
        $channelId = request('channel_id', $channel->id);
        $layout = request('layout');
        $pageTitle = request('page_title');
        $urlKey = request('url_key');
        $tagFilter = request('tag') ?: request('tag_ids');
        $createdFrom = request('created_from');
        $createdTo = request('created_to');
        $sortBy = request('sort', 'created_at');
        $sortOrder = request('order', 'desc');
        $limit = request('limit', 10);

        // Build optimized query using scopeQuery
        $pages = $this->pageRepository->scopeQuery(function ($query) use (
            $channelId, $locale, $layout, $pageTitle, $urlKey, $tagFilter, 
            $createdFrom, $createdTo, $sortBy, $sortOrder, $limit
        ) {
            // Eager load relationships to prevent N+1 queries
            $query->with([
                'translations' => function ($q) use ($locale) {
                    $q->where('locale', $locale)->select(['cms_page_id', 'locale', 'page_title', 'url_key', 'content', 'html_content', 'meta_title', 'meta_description', 'meta_keywords']);
                },
                'channels:id,name',
                'tags:id,name'
            ]);

            // Apply channel filter
            $query->whereHas('channels', function ($q) use ($channelId) {
                $q->where('channels.id', $channelId);
            });

            // Apply locale filter
            $query->whereHas('translations', function ($q) use ($locale) {
                $q->where('locale', $locale);
            });

            // Apply layout filter
            if ($layout) {
                $query->where('layout', $layout);
            }

            // Apply page title search
            if ($pageTitle) {
                $query->whereHas('translations', function ($q) use ($locale, $pageTitle) {
                    $q->where('locale', $locale)
                      ->where('page_title', 'like', '%' . $pageTitle . '%');
                });
            }

            // Apply URL key filter
            if ($urlKey) {
                $query->whereHas('translations', function ($q) use ($locale, $urlKey) {
                    $q->where('locale', $locale)
                      ->where('url_key', $urlKey);
                });
            }

            // Apply tag filters - support both 'tag' and 'tag_ids' parameters
            if ($tagFilter) {
                $tagIds = is_array($tagFilter) ? $tagFilter : explode(',', $tagFilter);
                $query->whereHas('tags', function ($q) use ($tagIds) {
                    $q->whereIn('cms_tags.id', $tagIds);
                });
            }

            // Apply date range filters
            if ($createdFrom) {
                $query->where('created_at', '>=', $createdFrom);
            }

            if ($createdTo) {
                $query->where('created_at', '<=', $createdTo);
            }

            // Apply sorting
            $allowedSortFields = ['id', 'layout', 'created_at', 'updated_at'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->orderBy('created_at', 'desc');
            }

            // Apply limit
            if ($limit > 0) {
                $query->limit($limit);
            }

            return $query;
        })->get();

        // Format data with optimized access to preloaded relationships
        $data = $pages->map(function ($page) use ($locale) {
            // Use already loaded translations (filtered by locale)
            $translation = $page->translations->first();
            
            // Use already loaded tags to avoid N+1 queries
            $tags = $page->tags ? $page->tags->pluck('name')->toArray() : [];
            
            return [
                'id' => $page->id,
                'page_title' => $translation->page_title ?? '',
                'url_key' => $translation->url_key ?? '',
                'content' => $translation->content ?? '',
                'html_content' => $translation->html_content ?? '',
                'meta_title' => $translation->meta_title ?? '',
                'meta_description' => $translation->meta_description ?? '',
                'meta_keywords' => $translation->meta_keywords ?? '',
                'layout' => $page->layout,
                'thumbnail' => $page->thumbnail ? asset('storage/' . $page->thumbnail) : null,
                'tags' => $tags,
                'url' => route('shop.cms.page', $translation->url_key ?? ''),
                'created_at' => $page->created_at,
                'updated_at' => $page->updated_at,
            ];
        });

        return response()->json([
            'data' => $data->values(),
        ]);
    }
}
