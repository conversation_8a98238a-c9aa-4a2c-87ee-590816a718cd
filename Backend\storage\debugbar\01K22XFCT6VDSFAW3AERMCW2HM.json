{"__meta": {"id": "01K22XFCT6VDSFAW3AERMCW2HM", "datetime": "2025-08-07 19:42:42", "utime": **********.630804, "method": "GET", "uri": "/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.383249, "end": **********.641834, "duration": 0.25858497619628906, "duration_str": "259ms", "measures": [{"label": "Booting", "start": **********.383249, "relative_start": 0, "end": **********.606375, "relative_end": **********.606375, "duration": 0.*****************, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.606387, "relative_start": 0.*****************, "end": **********.641836, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "35.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.619695, "relative_start": 0.*****************, "end": **********.623514, "relative_end": **********.623514, "duration": 0.003818988800048828, "duration_str": "3.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.628803, "relative_start": 0.*****************, "end": **********.628928, "relative_end": **********.628928, "duration": 0.00012493133544921875, "duration_str": "125μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.628942, "relative_start": 0.*****************, "end": **********.628955, "relative_end": **********.628955, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "259ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-12025475 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-12025475\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1418560685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1418560685\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1088003888 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IjVod251cWE5b3dLMFBvRUR0L1FEOEE9PSIsInZhbHVlIjoiR3BCY0JWWWJGKy9KZ3FUQkxaR3J4eGduRFpWbjNmT3Fscjd6L2d1MUJJbmNPbUQ2YjlBZTVqdmVDcDk4S0l4V0Nzb2E2MnNzRk5BWEp2djZsZWdHaU94ZEk2NURrM0FUN0hqOWJFNnorTXRFeHhycG43QVFDME1VTzVtZ3A4amQiLCJtYWMiOiJmNjFlNTYxOWU2ZTU1Nzk3M2Q4NjcyYWE5NmE4OGM5MDdlMDhlNGMzZTkzZjZhMWIzZjA3NTY0MTcxYWEzMzA5IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IncxUjVrZHFrSHlSZXo2VXdrOTNKNUE9PSIsInZhbHVlIjoiVUJKdmZ1amlXdnY1MTFKVG9BRDJKUmNTUzR5OW5pWWlRZytJY3lXODJGTGl3OXJhcENtQlFHQ1pmNVoyRE1KUUtObC9SQWNtYmxkWkFzZkdiZm8xdHRnUjYwV3ZiVHlqQ21aV1A1NDBKMVg2QlYzdmxoMkl6OGNXQVlmK1dFRngiLCJtYWMiOiI3NDI5NzBmNDgyZmQ4OGNlNTAwN2U3NzhmY2U2YmY0NjAzZDc0MTQ3ZmM2Nzk3YjgwMzc0ZDNkMDgzMzUwYjFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://mlk.test/?locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088003888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1731104690 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjVod251cWE5b3dLMFBvRUR0L1FEOEE9PSIsInZhbHVlIjoiR3BCY0JWWWJGKy9KZ3FUQkxaR3J4eGduRFpWbjNmT3Fscjd6L2d1MUJJbmNPbUQ2YjlBZTVqdmVDcDk4S0l4V0Nzb2E2MnNzRk5BWEp2djZsZWdHaU94ZEk2NURrM0FUN0hqOWJFNnorTXRFeHhycG43QVFDME1VTzVtZ3A4amQiLCJtYWMiOiJmNjFlNTYxOWU2ZTU1Nzk3M2Q4NjcyYWE5NmE4OGM5MDdlMDhlNGMzZTkzZjZhMWIzZjA3NTY0MTcxYWEzMzA5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IncxUjVrZHFrSHlSZXo2VXdrOTNKNUE9PSIsInZhbHVlIjoiVUJKdmZ1amlXdnY1MTFKVG9BRDJKUmNTUzR5OW5pWWlRZytJY3lXODJGTGl3OXJhcENtQlFHQ1pmNVoyRE1KUUtObC9SQWNtYmxkWkFzZkdiZm8xdHRnUjYwV3ZiVHlqQ21aV1A1NDBKMVg2QlYzdmxoMkl6OGNXQVlmK1dFRngiLCJtYWMiOiI3NDI5NzBmNDgyZmQ4OGNlNTAwN2U3NzhmY2U2YmY0NjAzZDc0MTQ3ZmM2Nzk3YjgwMzc0ZDNkMDgzMzUwYjFjIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731104690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-351751201 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">9430</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">44f0ecf702bf567366d9c4e958581d30</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 18:42:42 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351751201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1058213134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1058213134\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/211/HNgHFdS35kfHe5KQE2xEfIt9hNB0mlXkQmm47MaC.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}